import React from 'react';
import {
  Paper,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Button,
  Tabs,
  Tab,
  Box,
  CircularProgress,
  makeStyles,
} from '@material-ui/core';
import SendIcon from '@material-ui/icons/Send';
import { ApiRequest, HttpMethod } from '../../../../types';
import { TabPanel } from '../TabPanel';
import { ParamsTab } from '../ParamsTab';
import { HeadersTab } from '../HeadersTab';
import { BodyTab } from '../BodyTab';
import { AuthTab } from '../AuthTab';
import { PreRequestScriptPanel } from '../../PreRequestScriptPanel';
import { TestGeneratorPanel } from '../../TestGeneratorPanel';
import { getUrlHelperText } from '../../utils/requestUtils';

const useStyles = makeStyles(theme => ({
  paper: {
    padding: theme.spacing(2),
    height: '100%',
  },
  urlBar: {
    display: 'flex',
    alignItems: 'center',
    marginBottom: theme.spacing(2),
  },
  methodSelect: {
    width: 120,
    marginRight: theme.spacing(2),
  },
  urlField: {
    flexGrow: 1,
    marginRight: theme.spacing(2),
  },
  sendButton: {
    marginLeft: theme.spacing(1),
  },
}));

interface RequestBuilderProps {
  request: ApiRequest;
  onRequestChange: (request: ApiRequest) => void;
  onMethodChange: (event: React.ChangeEvent<{ value: unknown }>) => void;
  onUrlChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
  onSendRequest: () => void;
  onTabChange: (event: React.ChangeEvent<{}>, newValue: number) => void;
  onSavePreRequestScript: (script: string) => void;
  onRunTests: (testScript: string) => void;
  onSaveTests: (testScript: string) => void;
  tabValue: number;
  isLoading: boolean;
  isGeneratingTests: boolean;
  isRunningTests: boolean;
  isSavingPreRequestScript: boolean;
  testError: string | null;
  preRequestScriptError: string | null;
  currentResponse: any;
  testResults: any[];
}

export const RequestBuilder: React.FC<RequestBuilderProps> = ({
  request,
  onRequestChange,
  onMethodChange,
  onUrlChange,
  onSendRequest,
  onTabChange,
  onSavePreRequestScript,
  onRunTests,
  onSaveTests,
  tabValue,
  isLoading,
  isGeneratingTests,
  isRunningTests,
  isSavingPreRequestScript,
  testError,
  preRequestScriptError,
  currentResponse,
  testResults,
}) => {
  const classes = useStyles();

  const httpMethods: HttpMethod[] = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'HEAD', 'OPTIONS'];

  return (
    <Paper className={classes.paper}>
      {/* URL Bar */}
      <div className={classes.urlBar}>
        <FormControl className={classes.methodSelect}>
          <InputLabel>Method</InputLabel>
          <Select
            value={request.method}
            onChange={onMethodChange}
          >
            {httpMethods.map(method => (
              <MenuItem key={method} value={method}>
                {method}
              </MenuItem>
            ))}
          </Select>
        </FormControl>

        <TextField
          className={classes.urlField}
          label="URL"
          value={request.url}
          onChange={onUrlChange}
          placeholder="https://api.example.com/endpoint"
          helperText={getUrlHelperText(request.url)}
          variant="outlined"
          size="small"
        />

        <Button
          variant="contained"
          color="primary"
          className={classes.sendButton}
          startIcon={isLoading ? <CircularProgress size={20} color="inherit" /> : <SendIcon />}
          onClick={onSendRequest}
          disabled={isLoading || !request.url}
        >
          Send
        </Button>
      </div>

      {/* Request tabs */}
      <Tabs
        value={tabValue}
        onChange={onTabChange}
        indicatorColor="primary"
        textColor="primary"
      >
        <Tab label="Params" />
        <Tab label="Headers" />
        <Tab label="Body" />
        <Tab label="Auth" />
        <Tab label="Pre-request" />
        <Tab label="Tests" />
      </Tabs>

      {/* Params tab */}
      <TabPanel value={tabValue} index={0}>
        <ParamsTab
          params={request.params}
          onParamsChange={(params) => onRequestChange({ ...request, params })}
        />
      </TabPanel>

      {/* Headers tab */}
      <TabPanel value={tabValue} index={1}>
        <HeadersTab
          headers={request.headers}
          onHeadersChange={(headers) => onRequestChange({ ...request, headers })}
        />
      </TabPanel>

      {/* Body tab */}
      <TabPanel value={tabValue} index={2}>
        <BodyTab
          body={request.body}
          onBodyChange={(body) => onRequestChange({ ...request, body })}
        />
      </TabPanel>

      {/* Auth tab */}
      <TabPanel value={tabValue} index={3}>
        <AuthTab
          auth={request.auth}
          onAuthChange={(auth) => onRequestChange({ ...request, auth })}
        />
      </TabPanel>

      {/* Pre-request script tab */}
      <TabPanel value={tabValue} index={4}>
        <PreRequestScriptPanel
          script={request.preRequestScript || ''}
          onSave={onSavePreRequestScript}
          isSaving={isSavingPreRequestScript}
          error={preRequestScriptError}
        />
      </TabPanel>

      {/* Tests tab */}
      <TabPanel value={tabValue} index={5}>
        <TestGeneratorPanel
          request={request}
          response={currentResponse}
          onRunTests={onRunTests}
          onSaveTests={onSaveTests}
          isGenerating={isGeneratingTests}
          isRunning={isRunningTests}
          error={testError}
        />
      </TabPanel>
    </Paper>
  );
};
