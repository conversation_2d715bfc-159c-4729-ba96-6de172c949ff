import React from 'react';
import {
  Box,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Button,
  makeStyles,
} from '@material-ui/core';
import AddIcon from '@material-ui/icons/Add';
import SettingsIcon from '@material-ui/icons/Settings';
import { ApiEnvironment } from '../../../../types';

const useStyles = makeStyles(theme => ({
  environmentSelector: {
    display: 'flex',
    alignItems: 'center',
    marginBottom: theme.spacing(2),
  },
  envSelect: {
    flexGrow: 1,
    marginRight: theme.spacing(1),
  },
  envActions: {
    display: 'flex',
  },
}));

interface EnvironmentManagerProps {
  environments: ApiEnvironment[];
  currentEnvironment: string;
  onEnvironmentChange: (event: React.ChangeEvent<{ value: unknown }>) => void;
  onAddEnvironment: () => void;
  onManageEnvironments: () => void;
}

export const EnvironmentManager: React.FC<EnvironmentManagerProps> = ({
  environments,
  currentEnvironment,
  onEnvironmentChange,
  onAddEnvironment,
  onManageEnvironments,
}) => {
  const classes = useStyles();

  return (
    <Box className={classes.environmentSelector}>
      <FormControl className={classes.envSelect} size="small">
        <InputLabel>Environment</InputLabel>
        <Select
          value={currentEnvironment}
          onChange={onEnvironmentChange}
        >
          <MenuItem value="">
            <em>No Environment</em>
          </MenuItem>
          {environments.map(env => (
            <MenuItem key={env.id} value={env.id}>
              {env.name}
            </MenuItem>
          ))}
        </Select>
      </FormControl>
      
      <div className={classes.envActions}>
        <Button
          size="small"
          startIcon={<AddIcon />}
          onClick={onAddEnvironment}
        >
          Add
        </Button>
        <Button
          size="small"
          startIcon={<SettingsIcon />}
          onClick={onManageEnvironments}
        >
          Manage
        </Button>
      </div>
    </Box>
  );
};
