import React from 'react';
import {
  Paper,
  Typography,
  Tabs,
  Tab,
  Box,
  Chip,
  makeStyles,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableRow,
} from '@material-ui/core';
import { Alert } from '@material-ui/lab';
import { CodeSnippet } from '@backstage/core-components';
import { ApiResponse } from '../../../../types';
import { TabPanel } from '../TabPanel';
import { TestResultsPanel } from '../../TestResultsPanel';

const useStyles = makeStyles(theme => ({
  paper: {
    padding: theme.spacing(2),
    height: '100%',
  },
  responseHeader: {
    display: 'flex',
    alignItems: 'center',
    marginBottom: theme.spacing(2),
  },
  statusSuccess: {
    color: theme.palette.success.main,
  },
  statusError: {
    color: theme.palette.error.main,
  },
  statusInfo: {
    color: theme.palette.info.main,
  },
  statusWarning: {
    color: theme.palette.warning.main,
  },
  responseTime: {
    marginLeft: theme.spacing(2),
    fontSize: '0.875rem',
  },
  responseSize: {
    marginLeft: theme.spacing(1),
    fontSize: '0.875rem',
  },
}));

interface ResponseViewerProps {
  response: ApiResponse | null;
  responseTabValue: number;
  onResponseTabChange: (event: React.ChangeEvent<{}>, newValue: number) => void;
  testResults: any[];
  isRunningTests: boolean;
  testError: string | null;
}

export const ResponseViewer: React.FC<ResponseViewerProps> = ({
  response,
  responseTabValue,
  onResponseTabChange,
  testResults,
  isRunningTests,
  testError,
}) => {
  const classes = useStyles();

  const getStatusColor = (status: number): string => {
    if (status >= 200 && status < 300) return classes.statusSuccess;
    if (status >= 400 && status < 500) return classes.statusError;
    if (status >= 500) return classes.statusError;
    return classes.statusInfo;
  };

  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  if (!response) {
    return (
      <Paper className={classes.paper}>
        <Alert severity="info">
          Send a request to see the response here.
        </Alert>
      </Paper>
    );
  }

  return (
    <Paper className={classes.paper}>
      {/* Response header */}
      <div className={classes.responseHeader}>
        <Typography variant="h6">Response</Typography>
        <Chip
          label={`${response.status} ${response.statusText}`}
          className={getStatusColor(response.status)}
          size="small"
          style={{ marginLeft: 16 }}
        />
        <Typography variant="body2" className={classes.responseTime}>
          {response.time}ms
        </Typography>
        {response.size !== undefined && (
          <Typography variant="body2" className={classes.responseSize}>
            {formatBytes(response.size)}
          </Typography>
        )}
      </div>

      {/* Response tabs */}
      <Tabs
        value={responseTabValue}
        onChange={onResponseTabChange}
        indicatorColor="primary"
        textColor="primary"
      >
        <Tab label="Body" />
        <Tab label="Headers" />
        <Tab label="Tests" />
      </Tabs>

      {/* Body tab */}
      <TabPanel value={responseTabValue} index={0}>
        {response.body ? (
          <CodeSnippet
            text={response.body}
            language="json"
            showCopyCodeButton
          />
        ) : (
          <Alert severity="info">No response body</Alert>
        )}
      </TabPanel>

      {/* Headers tab */}
      <TabPanel value={responseTabValue} index={1}>
        {Object.keys(response.headers).length > 0 ? (
          <Table size="small">
            <TableHead>
              <TableRow>
                <TableCell>Header</TableCell>
                <TableCell>Value</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {Object.entries(response.headers).map(([key, value]) => (
                <TableRow key={key}>
                  <TableCell component="th" scope="row">
                    {key}
                  </TableCell>
                  <TableCell>{value}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        ) : (
          <Alert severity="info">No response headers</Alert>
        )}
      </TabPanel>

      {/* Tests tab */}
      <TabPanel value={responseTabValue} index={2}>
        <TestResultsPanel
          results={testResults}
          isRunning={isRunningTests}
          error={testError}
        />
      </TabPanel>
    </Paper>
  );
};
