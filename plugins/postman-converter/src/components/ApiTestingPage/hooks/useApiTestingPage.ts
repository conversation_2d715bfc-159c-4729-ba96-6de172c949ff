import { useCallback } from 'react';
import { useApi, errorApiRef } from '@backstage/core-plugin-api';
import { postmanConverterApiRef } from '../../../api';
import { ApiCollection, ApiEnvironment } from '../../../types';
import { convertApiCollectionToPostman } from '../utils/collectionUtils';

import { useCollections } from './useCollections';
import { useEnvironments } from './useEnvironments';
import { useRequest } from './useRequest';
import { useSnackbar } from './useSnackbar';
import { useDialogs } from './useDialogs';
import { useContextMenu } from './useContextMenu';
import { useSaveState } from './useSaveState';

/**
 * Main hook that combines all the functionality for the ApiTestingPage component
 */
export const useApiTestingPage = () => {
  const errorApi = useApi(errorApiRef);
  const postmanConverterApi = useApi(postmanConverterApiRef);

  // Initialize all sub-hooks
  const collectionsHook = useCollections();
  const environmentsHook = useEnvironments();
  const snackbarHook = useSnackbar();
  const dialogsHook = useDialogs();
  const contextMenuHook = useContextMenu();
  const saveStateHook = useSaveState();

  // Initialize request hook with collections data
  const requestHook = useRequest(collectionsHook.collections, collectionsHook.setCollections);

  // Handle saving a collection to the database
  const handleSaveCollection = useCallback(async (collectionId: string) => {
    const collection = collectionsHook.collections.find(c => c.id === collectionId);
    if (!collection) {
      snackbarHook.showError('Collection not found');
      return { success: false, error: 'Collection not found' };
    }

    saveStateHook.setSavingState(collectionId, true);

    try {
      // Convert ApiCollection to Postman collection format
      const postmanCollection = convertApiCollectionToPostman(collection);

      // Update the collection in the database
      await postmanConverterApi.updateCollection(collectionId, {
        name: collection.name,
        description: collection.description,
        content: JSON.stringify(postmanCollection)
      });

      // Mark as saved
      saveStateHook.markCollectionAsSaved(collectionId);
      snackbarHook.showSuccess(`Collection "${collection.name}" saved successfully`);

      return { success: true };
    } catch (error) {
      errorApi.post(error instanceof Error ? error : new Error(String(error)));
      snackbarHook.showError('Failed to save collection');
      return { success: false, error };
    } finally {
      saveStateHook.setSavingState(collectionId, false);
    }
  }, [collectionsHook.collections, postmanConverterApi, errorApi, snackbarHook, saveStateHook]);

  // Handle saving all collections with unsaved changes
  const handleSaveAllCollections = useCallback(async () => {
    const savePromises = Array.from(saveStateHook.unsavedCollections).map(collectionId =>
      handleSaveCollection(collectionId)
    );
    await Promise.all(savePromises);
  }, [saveStateHook.unsavedCollections, handleSaveCollection]);

  // Handle loading a request from a collection
  const handleLoadRequestFromCollection = useCallback((itemId: string) => {
    const collection = collectionsHook.collections.find(col => {
      return Object.keys(col.requests).includes(itemId);
    });

    if (collection && collection.requests[itemId]) {
      requestHook.setCurrentRequest(collection.requests[itemId]);
      collectionsHook.setSelectedItemId(itemId);
    }
  }, [collectionsHook.collections, requestHook.setCurrentRequest, collectionsHook.setSelectedItemId]);

  // Handle import collection
  const handleImportCollection = useCallback(async (apiCollection: ApiCollection) => {
    const result = await collectionsHook.handleImportCollection(apiCollection);
    if (result.success) {
      snackbarHook.showSuccess(`Collection "${apiCollection.name}" imported successfully`);
    } else {
      snackbarHook.showError('Failed to import collection');
    }
    return result;
  }, [collectionsHook.handleImportCollection, snackbarHook]);

  // Handle import environment
  const handleImportEnvironment = useCallback((environment: ApiEnvironment) => {
    const result = environmentsHook.handleImportEnvironment(environment);
    if (result.success) {
      snackbarHook.showSuccess(`Environment "${environment.name}" imported successfully`);
    }
    return result;
  }, [environmentsHook.handleImportEnvironment, snackbarHook]);

  // Handle sending request with current environment
  const handleSendRequestWithEnvironment = useCallback(async () => {
    const currentEnv = environmentsHook.environments.find(env => env.id === environmentsHook.currentEnvironment);
    const result = await requestHook.handleSendRequest(currentEnv);
    
    if (result.success) {
      snackbarHook.showSuccess('Request sent successfully');
    } else {
      snackbarHook.showError('Failed to send request');
    }
    
    return result;
  }, [environmentsHook.environments, environmentsHook.currentEnvironment, requestHook.handleSendRequest, snackbarHook]);

  return {
    // Collections
    ...collectionsHook,
    
    // Environments
    ...environmentsHook,
    
    // Request/Response
    ...requestHook,
    
    // UI State
    ...snackbarHook,
    ...dialogsHook,
    ...contextMenuHook,
    ...saveStateHook,
    
    // Combined handlers
    handleSaveCollection,
    handleSaveAllCollections,
    handleLoadRequestFromCollection,
    handleImportCollection,
    handleImportEnvironment,
    handleSendRequestWithEnvironment,
  };
};
