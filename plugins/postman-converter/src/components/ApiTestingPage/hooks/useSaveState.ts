import { useState, useCallback } from 'react';

export const useSaveState = () => {
  const [unsavedCollections, setUnsavedCollections] = useState<Set<string>>(new Set());
  const [isSaving, setIsSaving] = useState<Record<string, boolean>>({});

  const markCollectionAsUnsaved = useCallback((collectionId: string) => {
    setUnsavedCollections(prev => new Set([...prev, collectionId]));
  }, []);

  const markCollectionAsSaved = useCallback((collectionId: string) => {
    setUnsavedCollections(prev => {
      const newSet = new Set(prev);
      newSet.delete(collectionId);
      return newSet;
    });
  }, []);

  const setSavingState = useCallback((collectionId: string, saving: boolean) => {
    setIsSaving(prev => ({
      ...prev,
      [collectionId]: saving,
    }));
  }, []);

  const isCollectionSaving = useCallback((collectionId: string) => {
    return isSaving[collectionId] || false;
  }, [isSaving]);

  const hasUnsavedChanges = useCallback((collectionId: string) => {
    return unsavedCollections.has(collectionId);
  }, [unsavedCollections]);

  const getUnsavedCount = useCallback(() => {
    return unsavedCollections.size;
  }, [unsavedCollections]);

  const isAnySaving = useCallback(() => {
    return Object.values(isSaving).some(saving => saving);
  }, [isSaving]);

  return {
    unsavedCollections,
    isSaving,
    markCollectionAsUnsaved,
    markCollectionAsSaved,
    setSavingState,
    isCollectionSaving,
    hasUnsavedChanges,
    getUnsavedCount,
    isAnySaving,
  };
};
