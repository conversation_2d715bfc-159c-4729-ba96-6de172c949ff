import { useState, useCallback } from 'react';

export interface RenameDialogData {
  itemType: 'collection' | 'folder' | 'request';
  itemId: string;
  collectionId: string;
  currentName: string;
}

export const useDialogs = () => {
  // Dialog states
  const [importDialogOpen, setImportDialogOpen] = useState<boolean>(false);
  const [exportDialogOpen, setExportDialogOpen] = useState<boolean>(false);
  const [createFolderDialogOpen, setCreateFolderDialogOpen] = useState<boolean>(false);
  const [createRequestDialogOpen, setCreateRequestDialogOpen] = useState<boolean>(false);
  const [renameDialogOpen, setRenameDialogOpen] = useState<boolean>(false);
  const [editDialogOpen, setEditDialogOpen] = useState<boolean>(false);

  // Dialog data states
  const [renameDialogData, setRenameDialogData] = useState<RenameDialogData | null>(null);
  const [selectedCollectionForAction, setSelectedCollectionForAction] = useState<string>('');
  const [selectedFolderForAction, setSelectedFolderForAction] = useState<string>('');
  const [editCollectionId, setEditCollectionId] = useState<string>('');
  const [editCollectionName, setEditCollectionName] = useState<string>('');
  const [editCollectionDescription, setEditCollectionDescription] = useState<string>('');

  // Import dialog handlers
  const openImportDialog = useCallback(() => {
    setImportDialogOpen(true);
  }, []);

  const closeImportDialog = useCallback(() => {
    setImportDialogOpen(false);
  }, []);

  // Export dialog handlers
  const openExportDialog = useCallback(() => {
    setExportDialogOpen(true);
  }, []);

  const closeExportDialog = useCallback(() => {
    setExportDialogOpen(false);
  }, []);

  // Create folder dialog handlers
  const openCreateFolderDialog = useCallback((collectionId: string, parentFolderId?: string) => {
    setSelectedCollectionForAction(collectionId);
    setSelectedFolderForAction(parentFolderId || '');
    setCreateFolderDialogOpen(true);
  }, []);

  const closeCreateFolderDialog = useCallback(() => {
    setCreateFolderDialogOpen(false);
    setSelectedCollectionForAction('');
    setSelectedFolderForAction('');
  }, []);

  // Create request dialog handlers
  const openCreateRequestDialog = useCallback((collectionId: string, parentFolderId?: string) => {
    setSelectedCollectionForAction(collectionId);
    setSelectedFolderForAction(parentFolderId || '');
    setCreateRequestDialogOpen(true);
  }, []);

  const closeCreateRequestDialog = useCallback(() => {
    setCreateRequestDialogOpen(false);
    setSelectedCollectionForAction('');
    setSelectedFolderForAction('');
  }, []);

  // Rename dialog handlers
  const openRenameDialog = useCallback((data: RenameDialogData) => {
    setRenameDialogData(data);
    setRenameDialogOpen(true);
  }, []);

  const closeRenameDialog = useCallback(() => {
    setRenameDialogOpen(false);
    setRenameDialogData(null);
  }, []);

  // Edit collection dialog handlers
  const openEditDialog = useCallback((collectionId: string, name: string, description: string) => {
    setEditCollectionId(collectionId);
    setEditCollectionName(name);
    setEditCollectionDescription(description);
    setEditDialogOpen(true);
  }, []);

  const closeEditDialog = useCallback(() => {
    setEditDialogOpen(false);
    setEditCollectionId('');
    setEditCollectionName('');
    setEditCollectionDescription('');
  }, []);

  return {
    // Dialog states
    importDialogOpen,
    exportDialogOpen,
    createFolderDialogOpen,
    createRequestDialogOpen,
    renameDialogOpen,
    editDialogOpen,

    // Dialog data
    renameDialogData,
    selectedCollectionForAction,
    selectedFolderForAction,
    editCollectionId,
    editCollectionName,
    editCollectionDescription,

    // Dialog handlers
    openImportDialog,
    closeImportDialog,
    openExportDialog,
    closeExportDialog,
    openCreateFolderDialog,
    closeCreateFolderDialog,
    openCreateRequestDialog,
    closeCreateRequestDialog,
    openRenameDialog,
    closeRenameDialog,
    openEditDialog,
    closeEditDialog,

    // Setters for dialog data (for external updates)
    setEditCollectionName,
    setEditCollectionDescription,
  };
};
