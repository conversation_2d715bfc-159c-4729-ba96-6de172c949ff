import { useState, useCallback } from 'react';

export interface ContextMenuState {
  mouseX: number;
  mouseY: number;
  itemId: string;
  itemType: 'collection' | 'folder' | 'request';
}

export const useContextMenu = () => {
  const [contextMenu, setContextMenu] = useState<ContextMenuState | null>(null);

  const openContextMenu = useCallback((
    event: React.MouseEvent,
    itemId: string,
    itemType: 'collection' | 'folder' | 'request'
  ) => {
    event.preventDefault();
    event.stopPropagation();

    setContextMenu({
      mouseX: event.clientX - 2,
      mouseY: event.clientY - 4,
      itemId,
      itemType,
    });
  }, []);

  const closeContextMenu = useCallback(() => {
    setContextMenu(null);
  }, []);

  return {
    contextMenu,
    openContextMenu,
    closeContextMenu,
  };
};
